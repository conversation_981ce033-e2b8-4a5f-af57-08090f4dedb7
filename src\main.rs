#![no_std]
#![no_main]

use defmt::*;
use display_interface_spi::SPIInterface;
use embassy_executor::Spawner;
use embassy_stm32::{
    gpio::{Input, Level, Output, Pull, Speed},
    spi::{Config as SpiConfig, Spi},
};

use embedded_graphics::{
    image::{Image, ImageRaw},
    pixelcolor::BinaryColor,
    prelude::*,
};

use ssd1306::{Ssd1306, prelude::*, size::DisplaySize128x64};
use {defmt_rtt as _, panic_probe as _};

#[embassy_executor::main]
async fn main(_spawner: Spawner) {
    let p = embassy_stm32::init(Default::default());

    // 初始化三个GPIO引脚为软件SPI的SCK, MOSI, MISO

    // let miso = Input::new(p.PA3, Pull::None);
    let dc = Output::new(p.PB14, Level::Low, Speed::VeryHigh);
    let mut res = Output::new(p.PB12, Level::Low, Speed::VeryHigh);
    // 创建软件 SPI 实例，配置时钟延时和模式
    let spi = Spi::new_blocking_txonly(p.SPI2, p.PB13, p.PB15, SpiConfig::default());

    let interface = SPIInterface::new(spi, dc);
    let mut display = Ssd1306::new(interface, DisplaySize128x64, DisplayRotation::Rotate0)
        .into_buffered_graphics_mode();

    display
        .reset(&mut res, &mut embassy_time::Delay {})
        .unwrap();

    display.init();
    let raw: ImageRaw<BinaryColor> = ImageRaw::new(include_bytes!("./rust.raw"), 64);

    for i in (0..=64).chain((0..64).rev()).cycle() {
        let top_left = Point::new(i, 0);
        let im = Image::new(&raw, top_left);

        im.draw(&mut display).unwrap();

        display.flush().await.unwrap().await;

        display.clear(BinaryColor::Off).unwrap();
    }
}
